﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using MicrosoftStoreServicesSample.PersistentDB;

#nullable disable

namespace MicrosoftStoreServicesSample.Migrations
{
    [DbContext(typeof(ServerDBContext))]
    [Migration("20250722034028_InitialCreate10")]
    partial class InitialCreate10
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.18")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("MicrosoftStoreServicesSample.ClawbackV1QueueItem", b =>
                {
                    b.Property<string>("DbKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("ConsumeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("UserPurchaseId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("DbKey");

                    b.ToTable("ClawbackQueue");
                });

            modelBuilder.Entity("MicrosoftStoreServicesSample.CompletedConsumeTransaction", b =>
                {
                    b.Property<string>("DbKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("ConsumeDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderLineItemId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("QuantityConsumed")
                        .HasColumnType("int");

                    b.Property<string>("TrackingId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TransactionStatus")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("DbKey");

                    b.ToTable("CompletedConsumeTransactions");
                });

            modelBuilder.Entity("MicrosoftStoreServicesSample.Models.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoRenew")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset>("ExpirationTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("ExpirationTimeWithGrace")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("InAppOfferToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Price")
                        .HasColumnType("float");

                    b.Property<string>("ProductKind")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecurrenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoreId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("MicrosoftStoreServicesSample.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsVip")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserCollectionsId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserPurchaseId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("VipExpTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("MicrosoftStoreServicesSample.PendingConsumeRequest", b =>
                {
                    b.Property<string>("TrackingId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IncludeOrderIds")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUnmanagedConsumable")
                        .HasColumnType("bit");

                    b.Property<string>("ProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("RemoveQuantity")
                        .HasColumnType("bigint");

                    b.Property<string>("SandboxId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserCollectionsId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserPurchaseId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TrackingId");

                    b.ToTable("PendingConsumeRequests");
                });

            modelBuilder.Entity("MicrosoftStoreServicesSample.UserConsumableBalance", b =>
                {
                    b.Property<string>("DbKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("DbKey");

                    b.ToTable("UserBalances");
                });
#pragma warning restore 612, 618
        }
    }
}
