﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <UserSecretsId>d541f79d-2f04-47de-872e-00c9a8ea1afd</UserSecretsId>
    <Version>1.21.09</Version>
    <Authors><PERSON></Authors>
    <Company>Microsoft</Company>
    <Description>Sample Service to demonstrate calling the Microsoft Store Services and authenticating with them.</Description>
    <Copyright>Copyright (C) Microsoft Corporation. All rights reserved.</Copyright>
    <StartupObject>MicrosoftStoreServicesSample.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Microsoft_Store_Service_Configuration_and_Documentation.docx" />
    <None Remove="Readme.docx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Readme.docx">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Queues" Version="12.17.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="7.0.18" />
    <PackageReference Include="Microsoft.CorrelationVector" Version="1.0.42" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.18">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.18">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logging\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Microsoft-Store-Services-main\Microsoft.StoreServices\Microsoft.StoreServices\Microsoft.StoreServices.csproj" />
  </ItemGroup>


</Project>
