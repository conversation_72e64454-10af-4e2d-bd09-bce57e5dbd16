﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MicrosoftStoreServicesSample.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ClawbackQueue",
                columns: table => new
                {
                    DbKey = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    UserPurchaseId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConsumeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ClawbackQueue", x => x.DbKey);
                });

            migrationBuilder.CreateTable(
                name: "CompletedConsumeTransactions",
                columns: table => new
                {
                    DbKey = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    TransactionStatus = table.Column<int>(type: "int", nullable: false),
                    TrackingId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConsumeDate = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ProductId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OrderId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OrderLineItemId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    QuantityConsumed = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompletedConsumeTransactions", x => x.DbKey);
                });

            migrationBuilder.CreateTable(
                name: "PendingConsumeRequests",
                columns: table => new
                {
                    TrackingId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserPurchaseId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserCollectionsId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IncludeOrderIds = table.Column<bool>(type: "bit", nullable: false),
                    RemoveQuantity = table.Column<long>(type: "bigint", nullable: false),
                    IsUnmanagedConsumable = table.Column<bool>(type: "bit", nullable: false),
                    SandboxId = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PendingConsumeRequests", x => x.TrackingId);
                });

            migrationBuilder.CreateTable(
                name: "UserBalances",
                columns: table => new
                {
                    DbKey = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Quantity = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserBalances", x => x.DbKey);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ClawbackQueue");

            migrationBuilder.DropTable(
                name: "CompletedConsumeTransactions");

            migrationBuilder.DropTable(
                name: "PendingConsumeRequests");

            migrationBuilder.DropTable(
                name: "UserBalances");
        }
    }
}
