<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>添加用户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- 引入 layui.css -->
    <link href="//unpkg.com/layui@2.11.5/dist/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" href="../../css/main.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="addUserForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userId" lay-verify="required" placeholder="请输入用户ID" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="addUserSubmit">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 引入 layui.js -->
    <script src="//unpkg.com/layui@2.11.5/dist/layui.js"></script>
    <script>
        layui.config({
            base: '../../js/'
        }).extend({
            userApi: 'api'
        }).use(['form', 'layer', 'userApi'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var userApi = layui.userApi;
            var $ = layui.jquery;
            
            // 监听表单提交
            form.on('submit(addUserSubmit)', function(data) {
                var formData = data.field;
                
                userApi.addUser(formData.userId, function(err, res) {
                    if (err) {
                        layer.msg(err.message || '添加用户失败', {icon: 2});
                        return;
                    }
                    
                    layer.msg('添加用户成功', {icon: 1, time: 1000}, function() {
                        // 关闭当前页面
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                });
                
                return false; // 阻止表单跳转
            });
        });
    </script>
</body>
</html> 