<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑用户</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- 引入 layui.css -->
    <link href="//unpkg.com/layui@2.11.5/dist/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" href="../../css/main.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" lay-filter="editUserForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userId" readonly lay-verify="required" placeholder="请输入用户ID" autocomplete="off" class="layui-input layui-disabled">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">购买ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userPurchaseId" placeholder="请输入购买ID" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">收藏ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userCollectionsId" placeholder="请输入收藏ID" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">创建时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="createTime" readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">更新时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="lastUpdateTime" readonly class="layui-input layui-disabled">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" lay-submit lay-filter="editUserSubmit">立即提交</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 引入 layui.js -->
    <script src="//unpkg.com/layui@2.11.5/dist/layui.js"></script>
    <script>
        layui.config({
            base: '../../js/'
        }).extend({
            userApi: 'api'
        }).use(['form', 'layer', 'userApi'], function() {
            var form = layui.form;
            var layer = layui.layer;
            var userApi = layui.userApi;
            var $ = layui.jquery;
            
            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 加载用户数据
            var userId = getUrlParam('userId');
            if (userId) {
                userApi.getUser(userId, function(err, res) {
                    if (err) {
                        layer.msg(err.message || '获取用户信息失败', {icon: 2});
                        return;
                    }
                    
                    // 日期格式化
                    if (res.createTime) {
                        res.createTime = new Date(res.createTime).toLocaleString();
                    }
                    if (res.lastUpdateTime) {
                        res.lastUpdateTime = new Date(res.lastUpdateTime).toLocaleString();
                    }
                    
                    // 表单赋值
                    form.val('editUserForm', res);
                });
            }
            
            // 监听表单提交
            form.on('submit(editUserSubmit)', function(data) {
                var formData = data.field;
                
                // 提交表单数据
                var userData = {
                    userId: formData.userId,
                    userPurchaseId: formData.userPurchaseId,
                    userCollectionsId: formData.userCollectionsId
                };
                
                userApi.updateUser(userData, function(err, res) {
                    if (err) {
                        layer.msg(err.message || '更新用户失败', {icon: 2});
                        return;
                    }
                    
                    layer.msg('更新用户成功', {icon: 1, time: 1000}, function() {
                        // 关闭当前页面
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    });
                });
                
                return false; // 阻止表单跳转
            });
        });
    </script>
</body>
</html> 