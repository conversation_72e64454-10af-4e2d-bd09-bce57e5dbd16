{"format": 1, "restore": {"D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-Sample-main\\MicrosoftStoreServicesSample\\MicrosoftStoreServicesSample.csproj": {}}, "projects": {"D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\Microsoft.StoreServices.csproj": {"version": "1.21.9", "restore": {"projectUniqueName": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\Microsoft.StoreServices.csproj", "projectName": "Microsoft.StoreServices", "projectPath": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\Microsoft.StoreServices.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Azure.Storage.Queues": {"target": "Package", "version": "[12.17.1, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.18, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "jose-jwt": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-Sample-main\\MicrosoftStoreServicesSample\\MicrosoftStoreServicesSample.csproj": {"version": "1.21.9", "restore": {"projectUniqueName": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-Sample-main\\MicrosoftStoreServicesSample\\MicrosoftStoreServicesSample.csproj", "projectName": "MicrosoftStoreServicesSample", "projectPath": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-Sample-main\\MicrosoftStoreServicesSample\\MicrosoftStoreServicesSample.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-Sample-main\\MicrosoftStoreServicesSample\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\Microsoft.StoreServices.csproj": {"projectPath": "D:\\旧电脑\\MicrsoftStore\\Backend\\Microsoft-Store-Services-main\\Microsoft.StoreServices\\Microsoft.StoreServices\\Microsoft.StoreServices.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Azure.Storage.Queues": {"target": "Package", "version": "[12.17.1, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.CorrelationVector": {"target": "Package", "version": "[1.0.42, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.18, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.18, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}