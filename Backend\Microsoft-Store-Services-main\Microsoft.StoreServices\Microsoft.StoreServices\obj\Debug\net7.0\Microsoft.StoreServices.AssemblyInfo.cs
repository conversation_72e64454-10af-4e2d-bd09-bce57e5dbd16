//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Microsoft")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright (C) Microsoft Corporation. All rights reserved.")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"This client library enables working with the Microsoft Store Services to manage and validate user purchases within the Microsoft Store.  This library specifically has functionality to help request the needed Azure Active Directory access tokens and call the store services with this authorization.  More information can be found in the following articles:
          Monetization, engagement, and Store services - https://docs.microsoft.com/en-us/windows/uwp/monetize/
          Manage product entitlements from a service - https://docs.microsoft.com/en-us/windows/uwp/monetize/view-and-grant-products-from-a-service
")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("********")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.21.09")]
[assembly: System.Reflection.AssemblyProductAttribute("Microsoft.StoreServices")]
[assembly: System.Reflection.AssemblyTitleAttribute("Microsoft.StoreServices")]
[assembly: System.Reflection.AssemblyVersionAttribute("********")]

// 由 MSBuild WriteCodeFragment 类生成。

