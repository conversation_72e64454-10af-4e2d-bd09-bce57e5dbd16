using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.StoreServices;
using Microsoft.StoreServices.Collections;
using Microsoft.StoreServices.Collections.V9;
using MicrosoftStoreServicesSample.Models;
using MicrosoftStoreServicesSample.PersistentDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicrosoftStoreServicesSample.Controllers
{
    [ApiController]
    [Route("[controller]/[action]")]
    public class OrderController : ServiceControllerBase
    {
        private readonly IConfiguration _config;
        private readonly ServerDBContext _serverDBContext;

        public OrderController(IConfiguration config,
                                     ServerDBContext serverDBContext,
                                     IStoreServicesClientFactory storeServicesClientFactory,
                                     ILogger<OrderController> logger) :
            base(storeServicesClientFactory, logger)
        {
            _config = config;
            _serverDBContext = serverDBContext;
        }

        /// <summary>
        /// 添加订单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> AddOrder([FromBody] Order order)
        {
            if (string.IsNullOrWhiteSpace(order.Name))
            {
                return BadRequest("订单名称不能为空");
            }
            //if (order.Price <= 0)
            //{
            //    return BadRequest("订单价格必须大于0");
            //}
            if (string.IsNullOrWhiteSpace(order.StoreId) || string.IsNullOrWhiteSpace(order.UserId))
            {
                return BadRequest("商店ID和用户ID不能为空");
            }
            if (string.IsNullOrWhiteSpace(order.ProductKind))
            {
                return BadRequest("产品种类不能为空");
            }
            if (string.IsNullOrWhiteSpace(order.InAppOfferToken))
            {
                return BadRequest("加载项代码不能为空");
            }

            _serverDBContext.Orders.Add(order);
            await _serverDBContext.SaveChangesAsync();

            //TODO: 添加订单到队列，使用VerifyQuery方法验证订单是否有效

            return Ok(new { orderId = order.OrderId, result = true, message = "订单添加成功" });
        }

        /// <summary>
        /// 验证订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> VerifyOrder([FromQuery] string orderId)
        {
            if (string.IsNullOrWhiteSpace(orderId))
            {
                return BadRequest("订单ID不能为空");
            }

            var order = await VerifyQuery(orderId);

            return Ok(new { result = order });
        }

        /// <summary>
        /// 验证查询
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<bool> VerifyQuery(string orderId)
        {
            try
            {
                Order order = await _serverDBContext.Orders.FirstOrDefaultAsync(x => x.OrderId == orderId && x.Status == "pending") ?? throw new Exception("订单不存在");

                User user = await _serverDBContext.Users.FirstOrDefaultAsync(x => x.UserId == order.UserId) ?? throw new Exception("用户不存在");

                if (string.IsNullOrWhiteSpace(user.UserCollectionsId) || string.IsNullOrWhiteSpace(user.UserPurchaseId))
                {
                    order.Status = "failed";
                    await _serverDBContext.SaveChangesAsync();
                    return false;
                }

                //  构建我们的查询请求参数到集合服务
                var queryRequest = new CollectionsV9QueryRequest();
                //去除重复项，因为我们不希望在响应中收到重复的集合条目。
                queryRequest.ExcludeDuplicates = true;
                queryRequest.MaxPageSize = 200;

                // 首先，在响应体中添加受益人值
                // 使用 UserCollectionsId 限制结果范围至特定用户
                // 客户端已登录到商店。这将允许我们检索特定用户的集合。
                var beneficiary = new CollectionsRequestBeneficiary
                {
                    IdentityType = "b2b",
                    UserCollectionsId = user.UserCollectionsId,
                    LocalTicketReference = ""
                };
                queryRequest.Beneficiaries.Add(beneficiary);

                var skuId = new ProductSkuId
                {
                    ProductId = order.StoreId
                };
                queryRequest.ProductSkuIds.Add(skuId);

                var collectionsResponse = new CollectionsV9QueryResponse();
                var usersCollection = new List<CollectionsItemBase>();
                using (var storeClient = _storeServicesClientFactory.CreateClient())
                {
                    do
                    {
                        collectionsResponse = await storeClient.CollectionsV9QueryAsync(queryRequest);

                        //  如果有延续标记，则将其添加到下一个循环。
                        queryRequest.ContinuationToken = collectionsResponse.ContinuationToken;

                        // 在可能再发起请求获取剩余数据之前，将结果添加到我们的集合列表中
                        usersCollection = usersCollection.Concat(collectionsResponse.Items).ToList();
                    } while (collectionsResponse.ContinuationToken != null);
                }

                // 验证订单与用户所拥有的产品是否匹配
                if (usersCollection.Any(x => x.ProductId == order.StoreId
                && x.ProductKind == order.ProductKind
                && x.Status == "Active"))
                {
                    //这里处理订阅产品，不处理一次性消耗品
                    if (order.ProductKind == "Durable")
                    {
                        //如果一切正常的话，获取额外的信息，方便后续维护
                        var recurrenceRequest = new RecurrenceQueryRequest
                        {
                            UserPurchaseId = user.UserPurchaseId
                        };

                        var recurrenceResults = new RecurrenceQueryResponse();
                        using (var storeClient = _storeServicesClientFactory.CreateClient())
                        {
                            recurrenceResults = await storeClient.RecurrenceQueryAsync(recurrenceRequest);
                        }
                        RecurrenceItem recurrenceProduct = recurrenceResults.Items.First(x => x.ProductId == order.StoreId && x.RecurrenceState == "Active");

                        order.AutoRenew = recurrenceProduct.AutoRenew;
                        order.RecurrenceId = recurrenceProduct.Id;
                        order.ExpirationTime = recurrenceProduct.ExpirationTime;
                        order.ExpirationTimeWithGrace = recurrenceProduct.ExpirationTimeWithGrace;

                        // TODO: 到这里就可以写一下为用户开通服务的逻辑了
                        user.IsVip = true;
                        // 这里设置VIP过期时间，比实际订阅时间长一天，这样做的目的是为了防止订阅到期后，我们的定时器还没执行完，就导致用户VIP失效。
                        user.VipExpTime = order.ExpirationTime.DateTime + TimeSpan.FromDays(1);
                    }
                    else
                    {
                        //TODO: 处理一次性消耗品
                    }

                    order.Status = "succeed";
                    await _serverDBContext.SaveChangesAsync();

                    return true;
                }

                order.Status = "failed";
                await _serverDBContext.SaveChangesAsync();
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}