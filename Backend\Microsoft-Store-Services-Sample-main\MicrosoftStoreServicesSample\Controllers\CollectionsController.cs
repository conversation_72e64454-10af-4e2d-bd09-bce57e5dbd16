﻿//-----------------------------------------------------------------------------
// CollectionsController.cs
//
// Xbox Advanced Technology Group (ATG)
// Copyright (C) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License file under the project root for
// license information.
//-----------------------------------------------------------------------------

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.StoreServices;
using Microsoft.StoreServices.Collections;
using Microsoft.StoreServices.Collections.V8;
using Microsoft.StoreServices.Collections.V9;
using MicrosoftStoreServicesSample.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace MicrosoftStoreServicesSample.Controllers
{
    [ApiController]
    [Route("[controller]/[action]")]
    public class CollectionsController : ServiceControllerBase
    {
        private readonly IConfiguration _config;

        public CollectionsController(IConfiguration config,
                                     IStoreServicesClientFactory storeServicesClientFactory,
                                     ILogger<CollectionsController> logger) :
            base(storeServicesClientFactory, logger)
        {
            _config = config;
        }

        /// <summary>
        /// 将访问令牌发送给客户端，客户端需要这些令牌来创建
        /// 用于调用商店服务的功能性请求所需的 UserCollectionsId 和 UserPurchaseId。
        ///
        /// TODO：您可能需要将此流程整合到与客户端的授权握手中，
        /// 而不是使用单独的端点来分发这些令牌。
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<string>> RetrieveAccessTokens()
        {
            InitializeLoggingCv();
            var response = new ClientAccessTokensResponse
            {
                // TODO: 将此处获取和记录 UserId 的代码替换为你自己的用户身份验证 ID 系统。
                UserID = GetUserId()
            };

            try
            {
                response.AccessTokens = await GetAccessTokens();
            }
            catch (Exception e)
            {
                _logger.ServiceError(_cV.Value, "Error retrieving the access tokens", e);
                FinalizeLoggingCv();
                return "Error retrieving the access tokens";
            }

            // 将这些访问令牌发送给客户端，之后他们会获取 UserCollectionsId 和 UserPurchaseId 并将其返回给我们
            FinalizeLoggingCv();
            return new OkObjectResult(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 获取用户在 V8 B2BLicensePreview 中当前的 Collections 数据
        /// </summary>
        /// <param name="clientRequest">至少需要包含 UserCollectionsId</param>
        /// <returns>用户 Collections 数据的自定义格式文本</returns>
        [HttpPost]
        public async Task<ActionResult<string>> QueryV8([FromBody] ClientCollectionsQueryRequest clientRequest)
        {
            InitializeLoggingCv();
            var response = new StringBuilder("");

            bool err = false;

            //  TODO：用您自己的用户身份认证系统替换此代码来获取和记录UserId，或者让客户端将您能理解的ID作为UserPartnerID传入API中
            if (string.IsNullOrEmpty(GetUserId()))
            {
                response.AppendFormat("Missing {{UserId}} from Authorization header\n");
                err = true;
            }

            // 检查是否拥有此次操作的其他参数
            if (string.IsNullOrEmpty(clientRequest.UserCollectionsId))
            {
                response.AppendFormat("Request body missing CollectionsId. ex: {\"CollectionsId\": \"...\"}");
                err = true;
            }

            if (err)
            {
                // 请求错误，程序在此退出
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                _logger.QueryError(_cV.Value, GetUserId(), response.ToString(), null);
                return response.ToString();
            }

            bool includeJson = false;
            if (Request.Headers.ContainsKey("User-Agent"))
            {
                string userAgent = Request.Headers["User-Agent"];
                if (!string.IsNullOrEmpty(userAgent) && userAgent == "Microsoft.StoreServicesClientSample")
                {
                    //  此调用来自与该示例关联的客户端示例，因此在响应体中包含附加的 JSON，以便客户端使用这些值更新 UI。
                    includeJson = true;
                }
            }

            // 构建发送到集合服务的查询请求参数
            var queryRequest = new CollectionsV8QueryRequest();

            // 首先，在响应体中添加 beneficiary 值，
            // 该值使用 UserCollectionsId 以限定结果范围，
            // 范围限定为客户端登录到商店的用户。
            var beneficiary = new CollectionsRequestBeneficiary
            {
                IdentityType = "b2b",
                UserCollectionsId = clientRequest.UserCollectionsId,
                LocalTicketReference = ""
            };
            queryRequest.Beneficiaries.Add(beneficiary);

            if (!string.IsNullOrEmpty(clientRequest.Sbx))
            {
                queryRequest.SandboxId = clientRequest.Sbx;
            }

            if (clientRequest.EntitlementFilters != null &&
                clientRequest.EntitlementFilters.Count > 0)
            {
                queryRequest.EntitlementFilters = clientRequest.EntitlementFilters;
            }

            if (clientRequest.ProductIds != null)
            {
                foreach (var productId in clientRequest.ProductIds)
                {
                    var skuId = new ProductSkuId
                    {
                        ProductId = productId
                    };
                    queryRequest.ProductSkuIds.Add(skuId);
                }
            }

            // TODO: 添加您的服务所需的其他请求过滤
            // 例如，过滤特定的 ProductIds 或产品类型
            //
            // 在此示例中，我们将请求所有默认在 QueryRequest() 构造函数中配置的
            // 游戏（Game）、消耗品（Consumable）和耐用品（Durable）产品

            // 使用 StoreServicesClient 向 Collections 服务发送请求
            // 这段代码包含在 try/catch 中，用于记录任何异常并格式化
            // 返回给客户端的响应，去除调用堆栈信息。
            try
            {
                var collectionsResponse = new CollectionsV8QueryResponse();
                var usersCollection = new List<CollectionsItemBase>();
                using (var storeClient = _storeServicesClientFactory.CreateClient())
                {
                    do
                    {
                        collectionsResponse = await storeClient.CollectionsV8QueryAsync(queryRequest);

                        // 如果有续传令牌，则将其添加到下一周期。
                        queryRequest.ContinuationToken = collectionsResponse.ContinuationToken;

                        // 在可能发起另一请求以获取剩余部分之前，将结果追加到我们的集合列表中。

                        foreach (var item in collectionsResponse.Items)
                        {
                            usersCollection.Add(item);
                        }
                    } while (collectionsResponse.ContinuationToken != null);
                }

                // TODO：使用您的自定义逻辑处理结果
                // 本示例中，我们仅遍历结果，将其格式化为可读字符串，
                // 并将其发送回客户端以证明流程的通畅。
                response.Append(FormatCollectionsResponse(usersCollection));

                // 如果这是来自客户端示例，包含 JSON 以便能够在界面中正确显示项目
                if (includeJson)
                {
                    response.AppendLine("");
                    response.Append("RawResponse: ");
                    response.Append(JsonConvert.SerializeObject(collectionsResponse));
                }
            }
            catch (Exception ex)
            {
                _logger.QueryError(_cV.Value, GetUserId(), "Error querying collections v8.", ex);
                Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                response.AppendFormat("Unexpected error while querying the collections v8.  See logs for CV {0}", _cV.Value);
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// 获取用户当前的 Collections 数据，来自 V9 PublisherQuery
        /// </summary>
        /// <param name="clientRequest">至少需要包含 UserCollectionsId</param>
        /// <returns>用户 Collections 数据的自定义格式文本</returns>
        [HttpPost]
        public async Task<ActionResult<string>> QueryV9([FromBody] ClientCollectionsQueryRequest clientRequest)
        {
            InitializeLoggingCv();
            var response = new StringBuilder("");

            bool err = false;

            //  TODO: Replace this code obtaining and noting the UserId with your own
            //        authentication ID system for each user or have the client just
            //        put the ID you will understand into the API as the UserPartnerID
            if (string.IsNullOrEmpty(GetUserId()))
            {
                response.AppendFormat("Missing {{UserId}} from Authorization header\n");
                err = true;
            }

            //  Check that we have the other parameters for this operation
            if (string.IsNullOrEmpty(clientRequest.UserCollectionsId))
            {
                response.AppendFormat("Request body missing CollectionsId. ex: {\"CollectionsId\": \"...\"}");
                err = true;
            }

            //  Collections V9 requires that we provide the list of ProductIDs we want results for
            if (clientRequest.ProductIds == null ||
                clientRequest.ProductIds.Count == 0)
            {
                response.AppendFormat("Request body missing ProductIds. ex: {\"ProductIds\": [\"...\",\"...\"}");
                err = true;
            }

            if (err)
            {
                //  We had a bad request so exit here
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                _logger.QueryError(_cV.Value, GetUserId(), response.ToString(), null);
                return response.ToString();
            }

            bool includeJson = false;
            if (Request.Headers.ContainsKey("User-Agent"))
            {
                string userAgent = Request.Headers["User-Agent"];
                if (!string.IsNullOrEmpty(userAgent) && userAgent == "Microsoft.StoreServicesClientSample")
                {
                    //  This call is from the Client sample that is tied to this sample, so include the added JSON
                    //  in the response body so that it can use those values to update the UI.
                    includeJson = true;
                }
            }

            //  Build our query request parameters to the Collections Service
            var queryRequest = new CollectionsV9QueryRequest();

            //  First, add the beneficiary value in the response body that
            //  uses the UserCollectionsId to scope the results to the user
            //  signed into the store on the client.
            var beneficiary = new CollectionsRequestBeneficiary
            {
                IdentityType = "b2b",
                UserCollectionsId = clientRequest.UserCollectionsId,
                LocalTicketReference = ""
            };
            queryRequest.Beneficiaries.Add(beneficiary);

            if (!string.IsNullOrEmpty(clientRequest.Sbx))
            {
                queryRequest.SandboxId = clientRequest.Sbx;
            }

            foreach (var productId in clientRequest.ProductIds)
            {
                var skuId = new ProductSkuId
                {
                    ProductId = productId
                };
                queryRequest.ProductSkuIds.Add(skuId);
            }

            //  TODO: Add any other request filtering that your service requires
            //        For example, status filtering

            //  Send the request to the Collections service using a StoreServicesClient
            //  This is wrapped in a try/catch to log any exceptions and to format
            //  the response to the client to remove call stack info.
            try
            {
                var collectionsResponse = new CollectionsV9QueryResponse();
                var usersCollection = new List<CollectionsItemBase>();
                using (var storeClient = _storeServicesClientFactory.CreateClient())
                {
                    do
                    {
                        collectionsResponse = await storeClient.CollectionsV9QueryAsync(queryRequest);

                        //  If there was a continuation token add it to the next cycle.
                        queryRequest.ContinuationToken = collectionsResponse.ContinuationToken;

                        //  Append the results to our collections list before possibly doing
                        //  another request to get the rest.
                        usersCollection = usersCollection.Concat(collectionsResponse.Items).ToList();
                    } while (collectionsResponse.ContinuationToken != null);
                }

                //  TODO: Operate on the results with your custom logic
                //        For this sample we just iterate through the results, format them to
                //        a readable string and send it back to the client as proof of flow.
                response.Append(FormatCollectionsResponse(usersCollection));

                //  If this is from the Client sample, include the JSON so that it can display the items in the UI
                //  properly
                if (includeJson)
                {
                    response.AppendLine("");
                    response.Append("RawResponse: ");
                    response.Append(JsonConvert.SerializeObject(collectionsResponse));
                }
            }
            catch (Exception ex)
            {
                _logger.QueryError(_cV.Value, GetUserId(), "Error querying collections v9.", ex);
                Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                response.AppendFormat("Unexpected error while querying the collections v9.  See logs for CV {0}", _cV.Value);
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// Takes in the generic CollectionsItems list from a call to Collections v8 or v9 and
        /// formats the information into text that can be displayed in the client sample's
        /// debug output
        /// </summary>
        /// <param name="CollectionsItems"></param>
        /// <returns>string</returns>
        private string FormatCollectionsResponse(List<CollectionsItemBase> CollectionsItems)
        {
            var response = new StringBuilder("");
            var trialData = new StringBuilder("");
            bool includeTrialData = false;

            response.Append(
                    "| ProductId    | Qty | Product Kind | Acquisition | IsTrial | Satisfied By |\n" +
                    "|--------------------------------------------------------------------------|\n");

            foreach (var item in CollectionsItems)
            {
                //  Some Durable types have a quantity of 1, but for the output we will only show the
                //  quantity if this is a consumable type product
                string quantityToDisplay = "";
                if (item.ProductKind == "UnmanagedConsumable" ||
                    item.ProductKind == "Consumable")
                {
                    quantityToDisplay = item.Quantity.ToString();
                }

                string formattedType = item.ProductKind;

                if (item.ProductKind == "UnmanagedConsumable")
                {
                    formattedType = "U.Consumable";
                }

                response.AppendFormat("| {0,-12} | {1,-3} | {2,-12} | {3,-11} | {4,-7} ",
                                        item.ProductId,
                                        quantityToDisplay,
                                        formattedType,
                                        item.AcquisitionType,
                                        item.TrialData.IsTrial);

                //  Check if this is enabled because of a satisfying entitlement from a bundle or subscription
                //  format to add those to the output on their own lines.
                if (item.SatisfiedByProductIds.Any())
                {
                    bool isFirstEntitlement = true;
                    foreach (var parent in item.SatisfiedByProductIds)
                    {
                        if (isFirstEntitlement)
                        {
                            isFirstEntitlement = false;
                            response.AppendFormat("| {0,-12} |\n",
                                                  parent);
                        }
                        else
                        {
                            response.AppendFormat("|                                                   {0,-12} |\n",
                                                  parent);
                        }
                    }
                }
                else
                {
                    response.AppendFormat("|              |\n");
                }

                if (item.TrialData.IsTrial)
                {
                    if (!includeTrialData)
                    {
                        includeTrialData = true;
                        trialData.Append(
                        "| ProductId    | IsInTrialPeriod | Remaining (DD.HH:MM:SS)        |\n" +
                        "|-----------------------------------------------------------------|\n");
                    }

                    string remainingTrialTimeText = string.Format("{0}.{1}:{2}:{3}",
                                                                 item.TrialData.TrialTimeRemaining.Days,
                                                                 item.TrialData.TrialTimeRemaining.Hours,
                                                                 item.TrialData.TrialTimeRemaining.Minutes,
                                                                 item.TrialData.TrialTimeRemaining.Seconds);

                    trialData.AppendFormat("| {0,-12} | {1,-15} | {2,-30} |\n",
                                           item.ProductId,
                                           item.TrialData.IsInTrialPeriod,
                                           remainingTrialTimeText);
                }
            }

            if (includeTrialData)
            {
                response.AppendLine("");
                response.Append(trialData);
            }

            return response.ToString();
        }

        /// <summary>
        /// 消耗指定数量的可消耗产品 productID
        /// </summary>
        /// <param name="clientRequest">需要 ProductId、Quantity、UserCollectionsId（对于消耗请求）和 UserPurchaseId（对于回扣验证）</param>
        /// <returns>自定义格式的文本，表示消耗请求的结果</returns>
        [HttpPost]
        public async Task<ActionResult<string>> Consume([FromBody] ClientConsumeRequest clientRequest)
        {
            //  Must call this to get the cV for this call flow
            InitializeLoggingCv();
            var response = new StringBuilder("");

            PendingConsumeRequest pendingRequest;
            var consumeManager = new ConsumableManager(_config, _storeServicesClientFactory, _logger);

            try
            {
                //  TODO: Replace this code obtaining and noting the UserId with your own
                //        authentication ID system for each user or have the client just
                //        put the ID you will understand into the API as the UserPartnerID
                if (string.IsNullOrEmpty(GetUserId()))
                {
                    throw new ArgumentException("No UserId in request header", nameof(clientRequest));
                }

                pendingRequest = ConsumableManager.CreateAndVerifyPendingConsumeRequest(clientRequest);
            }
            catch (ArgumentException ex)
            {
                //  We had a bad request so exit here
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                _logger.ConsumeError(_cV.Value, GetUserId(), "", "", 0, ex.Message, ex);
                return ex.Message;
            }

            //  call our helper function to manage the call controller
            response.Append(await consumeManager.ConsumeAsync(pendingRequest, _cV));

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        ////////////////////////////////////////////////////////////////////
        //  测试端点 - 不适用于零售发布
        ////////////////////////////////////////////////////////////////////
        //  TODO: 如果您将此作为框架使用，请移除这些 API
        // 用以构建您的服务。这些仅是测试端点
        //        以示例方式展示服务如何处理待处理的消费请求和反向搜索
        //        以及回收搜索请求。
        ////////////////////////////////////////////////////////////////////

        /// <summary>
        /// 注意：此为测试 API，不应包含在生产部署中
        ///
        /// 向缓存中添加一个待处理的消费项，以模拟虽然发起了请求但尚未收到响应的情况
        /// 未收到响应，我们需要重发请求以查看结果。
        /// </summary>
        /// <param name="clientRequest">需要 ProductId、Quantity、UserCollectionsId（对于消耗请求）和 UserPurchaseId（对于回扣验证）</param>
        /// <returns>自定义格式的文本，表示消耗请求的结果</returns>
        [HttpPost]
        public async Task<ActionResult<string>> AddPendingConsume([FromBody] ClientConsumeRequest clientRequest)
        {
            //  Must call this to get the cV for this call flow
            InitializeLoggingCv();
            var response = new StringBuilder("");
            var pendingRequest = new PendingConsumeRequest();

            try
            {
                //  TODO: Replace this code obtaining and noting the UserId with your own
                //        authentication ID system for each user or have the client just
                //        put the ID you will understand into the API as the UserPartnerID
                if (string.IsNullOrEmpty(GetUserId()))
                {
                    throw new ArgumentException("No UserId in request header", nameof(clientRequest));
                }

                pendingRequest = ConsumableManager.CreateAndVerifyPendingConsumeRequest(clientRequest);
            }
            catch (ArgumentException ex)
            {
                //  We had a bad request so exit here
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                _logger.ConsumeError(_cV.Value,
                                     GetUserId(),
                                     pendingRequest.TrackingId,
                                     pendingRequest.ProductId,
                                     pendingRequest.RemoveQuantity,
                                     ex.Message,
                                     ex);
                return new OkObjectResult("Error consuming the request");
            }

            //  This is only a test function here so that we can cache
            //  some pending consumes as if we tried but did not get a
            //  response back.  You can then call the RetryPendingConsumes
            //  endpoint to validate how the service would handle this
            //  scenario.
            var consumeManager = new ConsumableManager(_config, _storeServicesClientFactory, _logger);
            await consumeManager.TrackPendingConsumeAsync(pendingRequest, _cV);

            response.Append("Consume added to Pending cache, but not sent to Collections\n");
            response.AppendFormat("TrackingId:{0}\nUser:{1}\nConsumable:{2}\nQuantity:{3}\nSandboxId:{4}",
                                  pendingRequest.TrackingId,
                                  pendingRequest.UserId,
                                  pendingRequest.ProductId,
                                  pendingRequest.RemoveQuantity,
                                  pendingRequest.SandboxId);

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// 注意：此为测试 API，不应包含在生产部署中
        ///
        /// 返回当前所有已消耗产品的余额给调用者
        /// 服务器正在跟踪的 UserIds
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult<string> ViewPendingConsumes()
        {
            InitializeLoggingCv();

            var response = new StringBuilder("Pending consume requests:\n");

            var consumeManager = new ConsumableManager(_config, _storeServicesClientFactory, _logger);
            var pendingConsumes = consumeManager.GetAllPendingRequests(_cV);

            foreach (var consumeRequest in pendingConsumes)
            {
                response.AppendFormat("TrackingId {0} for UserId {1} on product {2} quantity of {3} in {4}\n",
                                      consumeRequest.TrackingId,
                                      consumeRequest.UserId,
                                      consumeRequest.ProductId,
                                      consumeRequest.RemoveQuantity,
                                      consumeRequest.SandboxId);
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// 注意：此接口仅用于测试，不应用于生产环境部署。
        /// 查找所有未完成的消耗品待处理交易，并尝试重新处理每一笔交易。此接口仅供测试使用，实际服务部署中不会对外开放。
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<string>> RetryPendingConsumes()
        {
            //  Must call this to get the cV for this call flow
            InitializeLoggingCv();

            var response = new StringBuilder("");

            response.AppendFormat("Finding all pending consume calls...\n");

            var consumeManager = new ConsumableManager(_config, _storeServicesClientFactory, _logger);
            List<PendingConsumeRequest> pendingUserConsumeRequests = consumeManager.GetAllPendingRequests(_cV);

            response.AppendFormat("Found {0} pending consume request(s) to complete or verify...\n", pendingUserConsumeRequests.Count);
            foreach (var currentRequest in pendingUserConsumeRequests)
            {
                //  Make the actual consume call
                response.Append(await consumeManager.ConsumeAsync(currentRequest, _cV));
            }

            var finalResponse = response.ToString();
            _logger.RetryPendingConsumesResponse(_cV.Increment(), finalResponse);

            FinalizeLoggingCv();
            return new OkObjectResult(finalResponse);
        }

        /// <summary>
        /// 注意：此接口仅用于测试，不应作为生产环境部署的一部分
        ///
        /// 根据服务器跟踪的用户ID，返回调用方所有已消费产品的当前余额
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult<string> ViewUserBalances()
        {
            InitializeLoggingCv();

            var consumeManager = new ConsumableManager(_config, _storeServicesClientFactory, _logger);
            var userBalances = consumeManager.GetAllUserBalances(_cV);
            var response = new StringBuilder("User balances from consumed items:\n");

            foreach (var userBalance in userBalances)
            {
                response.AppendFormat("User {0}'s balance of {1} is {2}\n",
                                      userBalance.UserId,
                                      userBalance.ProductId,
                                      userBalance.Quantity);
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }
    }
}