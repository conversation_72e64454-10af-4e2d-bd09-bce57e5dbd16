# Microsoft Store 用户管理前端

这是一个基于layui框架开发的Microsoft Store用户管理前端界面，用于与Microsoft Store服务后端进行交互，实现用户的增删改查等管理功能。

## 功能特性

- 用户列表展示与分页
- 用户信息查看
- 用户添加
- 用户编辑
- 用户删除
- 用户访问令牌获取

## 目录结构

```
Frontend/
├── css/                    # 样式文件
│   └── main.css            # 主样式文件
├── js/                     # JavaScript文件
│   ├── main.js             # 主脚本文件
│   └── api.js              # API接口文件
├── pages/                  # 页面文件
│   └── user/               # 用户管理页面
│       ├── list.html       # 用户列表页面
│       ├── add.html        # 添加用户页面
│       ├── edit.html       # 编辑用户页面
│       └── detail.html     # 用户详情页面
└── index.html              # 主页面
```

## 使用说明

1. 确保后端API服务已启动，默认地址为`https://localhost:53804`
2. 使用Web服务器托管前端文件夹，或直接打开`index.html`文件
3. 在页面中可以进行用户管理操作

## 配置说明

在`js/main.js`文件中，可以修改API的基础URL地址：

```javascript
// 配置
var config = {
    base_url: 'https://localhost:53804', // API基础URL，根据实际情况修改
    version: '1.0.0'
};
```

## API接口

前端通过`js/api.js`中定义的以下API接口与后端进行交互：

- `addUser(userId, callback)` - 添加用户
- `getUser(userId, callback)` - 获取用户信息
- `updateUser(userData, callback)` - 更新用户信息
- `deleteUser(userId, callback)` - 删除用户
- `getUserAccessTokens(userId, callback)` - 获取用户访问令牌

## 开发说明

本项目使用layui框架v2.11.5版本开发，如需更新或修改，请参考[layui官方文档](https://www.layui.com/doc/)。

## 浏览器兼容性

- Chrome (最新版)
- Firefox (最新版)
- Safari (最新版)
- Edge (最新版)
- IE 11+ 