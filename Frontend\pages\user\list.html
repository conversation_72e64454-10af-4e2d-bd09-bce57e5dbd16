<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="https://www.layuicdn.com/layui-v2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../../css/main.css" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="user-btn-group">
                    <button class="layui-btn layui-btn-normal" id="addUserBtn">
                        <i class="layui-icon">&#xe654;</i> 添加用户
                    </button>
                    <button class="layui-btn layui-btn-primary" id="refreshBtn">
                        <i class="layui-icon">&#xe669;</i> 刷新
                    </button>
                </div>
                
                <div class="user-search-box">
                    <div class="layui-form layui-form-pane">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">用户ID</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="userId" placeholder="请输入用户ID" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="searchBtn">
                                    <i class="layui-icon">&#xe615;</i> 搜索
                                </button>
                                <button class="layui-btn layui-btn-primary" id="resetBtn">
                                    <i class="layui-icon">&#xe640;</i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <table id="userTable" lay-filter="userTable"></table>
                
                <script type="text/html" id="userTableBar">
                    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
                    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="token">获取令牌</a>
                </script>
            </div>
        </div>
    </div>
    
    <script src="https://www.layuicdn.com/layui-v2.6.8/layui.js"></script>
    <script>
        layui.config({
            base: '../../js/'
        }).extend({
            userApi: 'api'
        }).use(['table', 'layer', 'form', 'userApi'], function() {
            var table = layui.table;
            var layer = layui.layer;
            var form = layui.form;
            var userApi = layui.userApi;
            var $ = layui.jquery;
            
            // 表格实例
            var tableIns = table.render({
                elem: '#userTable',
                url: userApi.getApiConfig().base_url + '/User/GetUsers', // 使用URL方式加载数据
                page: true,
                limit: 10,
                limits: [10, 20, 50, 100],
                cols: [[
                    {field: 'userId', title: '用户ID', width: 180},
                    {field: 'userPurchaseId', title: '购买ID', width: 200},
                    {field: 'userCollectionsId', title: '收藏ID', width: 200},
                    {field: 'createTime', title: '创建时间', width: 180, sort: true, templet: function(d) {
                        return d.createTime ? new Date(d.createTime).toLocaleString() : '';
                    }},
                    {field: 'lastUpdateTime', title: '更新时间', width: 180, sort: true, templet: function(d) {
                        return d.lastUpdateTime ? new Date(d.lastUpdateTime).toLocaleString() : '';
                    }},
                    {title: '操作', width: 280, align: 'center', fixed: 'right', toolbar: '#userTableBar'}
                ]],
                parseData: function(res) {
                    // 将后端返回的数据格式转换为layui table要求的格式
                    return {
                        "code": 0, // layui table规定0代表成功
                        "msg": res.message || "", // 错误信息
                        "count": res.total, // 数据总数
                        "data": res.items // 数据列表
                    };
                },
                request: {
                    pageName: 'page', // 后端接收的页码参数名
                    limitName: 'limit' // 后端接收的每页数量参数名
                }
            });
            
            // 工具条事件
            table.on('tool(userTable)', function(obj) {
                var data = obj.data;
                var event = obj.event;
                
                if (event === 'detail') {
                    layer.open({
                        type: 2,
                        title: '用户详情',
                        content: 'detail.html?userId=' + data.userId,
                        area: ['800px', '600px'],
                        maxmin: true
                    });
                } else if (event === 'edit') {
                    layer.open({
                        type: 2,
                        title: '编辑用户',
                        content: 'edit.html?userId=' + data.userId,
                        area: ['800px', '600px'],
                        maxmin: true,
                        end: function() {
                            tableIns.reload(); // 弹窗关闭后重载表格
                        }
                    });
                } else if (event === 'del') {
                    layer.confirm('确定要删除该用户吗？', function(index) {
                        userApi.deleteUser(data.userId, function(err, res) {
                            if (err) {
                                layer.msg(err.message || '删除失败', {icon: 2});
                                return;
                            }
                            layer.msg('删除成功', {icon: 1});
                            tableIns.reload(); // 删除成功后重载表格
                        });
                        layer.close(index);
                    });
                } else if (event === 'token') {
                    userApi.getUserAccessTokens(data.userId, function(err, res) {
                        if (err) {
                            layer.msg(err.message || '获取令牌失败', {icon: 2});
                            return;
                        }
                        
                        var tokenHtml = '<div class="layui-text">';
                        tokenHtml += '<p><strong>用户ID:</strong> ' + res.userId + '</p>';
                        tokenHtml += '<p><strong>访问令牌:</strong></p>';
                        tokenHtml += '<textarea class="layui-textarea" style="height: 150px;">' + JSON.stringify(res.accessTokens, null, 2) + '</textarea>';
                        tokenHtml += '</div>';
                        
                        layer.open({
                            type: 1,
                            title: '用户访问令牌',
                            content: tokenHtml,
                            area: ['600px', '400px']
                        });
                    });
                }
            });
            
            // 添加用户按钮事件
            $('#addUserBtn').on('click', function() {
                layer.open({
                    type: 2,
                    title: '添加用户',
                    content: 'add.html',
                    area: ['800px', '600px'],
                    maxmin: true,
                    end: function() {
                        tableIns.reload(); // 弹窗关闭后重载表格
                    }
                });
            });
            
            // 刷新按钮事件
            $('#refreshBtn').on('click', function() {
                tableIns.reload();
            });
            
            // 搜索按钮事件
            $('#searchBtn').on('click', function() {
                var userId = $('input[name="userId"]').val();
                tableIns.reload({
                    where: {
                        userId: userId // 将搜索条件作为where参数传递
                    },
                    page: {
                        curr: 1 // 重新从第1页开始
                    }
                });
            });
            
            // 重置按钮事件
            $('#resetBtn').on('click', function() {
                $('input[name="userId"]').val('');
                tableIns.reload({
                    where: {
                        userId: ''
                    },
                    page: {
                        curr: 1
                    }
                });
            });
        });
    </script>
</body>
</html> 