﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MicrosoftStoreServicesSample", "MicrosoftStoreServicesSample\MicrosoftStoreServicesSample.csproj", "{24F2D01D-DB8B-4C1B-AAFC-619400D086AC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Microsoft.StoreServices", "..\Microsoft-Store-Services-main\Microsoft.StoreServices\Microsoft.StoreServices\Microsoft.StoreServices.csproj", "{C7FC32A3-E6A9-FAB6-82B0-19F5CF233914}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "StoreServices_UnitTests", "..\Microsoft-Store-Services-main\Microsoft.StoreServices\StoreServices_UnitTests\StoreServices_UnitTests.csproj", "{B18BBCEA-8B57-2C5E-E88D-36704339D07D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{24F2D01D-DB8B-4C1B-AAFC-619400D086AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24F2D01D-DB8B-4C1B-AAFC-619400D086AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24F2D01D-DB8B-4C1B-AAFC-619400D086AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24F2D01D-DB8B-4C1B-AAFC-619400D086AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7FC32A3-E6A9-FAB6-82B0-19F5CF233914}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7FC32A3-E6A9-FAB6-82B0-19F5CF233914}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7FC32A3-E6A9-FAB6-82B0-19F5CF233914}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7FC32A3-E6A9-FAB6-82B0-19F5CF233914}.Release|Any CPU.Build.0 = Release|Any CPU
		{B18BBCEA-8B57-2C5E-E88D-36704339D07D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B18BBCEA-8B57-2C5E-E88D-36704339D07D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B18BBCEA-8B57-2C5E-E88D-36704339D07D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B18BBCEA-8B57-2C5E-E88D-36704339D07D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F2DF3B46-B7B7-4A45-BB7F-C63A08FEB276}
	EndGlobalSection
EndGlobal
