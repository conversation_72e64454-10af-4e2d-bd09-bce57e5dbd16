﻿//-----------------------------------------------------------------------------
// PurchaseController.cs
//
// Xbox Advanced Technology Group (ATG)
// Copyright (C) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License file under the project root for
// license information.
//-----------------------------------------------------------------------------

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.StoreServices;
using MicrosoftStoreServicesSample.Models;
using MicrosoftStoreServicesSample.PersistentDB;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicrosoftStoreServicesSample.Controllers
{
    /// <summary>
    /// 示例端点，用于演示使用该功能的能力
    /// 购买端点以管理订阅（重复购买）和
    /// 用于监控和管理向客户退款（追回）
    /// </summary>
    [ApiController]
    [Route("[controller]/[action]")]
    public class PurchaseController : ServiceControllerBase
    {
        private readonly IConfiguration _config;

        public PurchaseController(IConfiguration config,
                                  IStoreServicesClientFactory storeServicesClientFactory,
                                  ILogger<CollectionsController> logger) : base(storeServicesClientFactory, logger)
        {
            _config = config;
        }

        /// <summary>
        /// 你可能希望将此流程整合到你的授权机制中
        /// 与客户端进行握手而不是使用专用端点进行握手
        /// 输出这些内容。
        ///
        /// 向客户端发送访问令牌，客户端将使用这些令牌创建
        /// 必须提供 UserCollectionsId 和 UserPurchaseId 才能调用功能方法
        /// 作出的商店服务相关操作。
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<string>> AccessTokens()
        {
            InitializeLoggingCv();
            var response = new ClientAccessTokensResponse
            {
                // TODO: 替换此代码以获取并记录 UserId 为您的代码
                // 为每位用户建立身份认证系统。
                // 为这个客户端生成一个唯一的 ID
                UserID = GetUserId()
            };

            try
            {
                response.AccessTokens = await GetAccessTokens();
            }
            catch (Exception e)
            {
                return e.Message;
            }

            // 将这些访问令牌发送给客户端，然后
            // 客户端获取 UserCollectionsId 和 UserPurchaseId 后返回
            // 给我们
            FinalizeLoggingCv();
            return new OkObjectResult(JsonConvert.SerializeObject(response));
        }

        //////////////////////////////////////////////////////////////////////
        // 测试端点 - 不适用于零售发布
        //////////////////////////////////////////////////////////////////////
        // TODO: 如果您将此作为框架使用，请移除这些 API
        // 用以构建您的服务。这些仅是测试端点
        // 用于演示如何使用 Purchase 服务。这些
        // 通常只会从您自己的地方使用和控制
        // 用于对账或退款的服务
        ////////////////////////////////////////////////////////////////////

        /// <summary>
        /// 返回所提供的用户购买ID对应的任何订阅信息
        /// </summary>
        /// <param name="clientRequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<string>> RecurrenceQuery([FromBody] ClientPurchaseId clientRequest)
        {
            //  Check that we have a properly formatted request body
            if (string.IsNullOrEmpty(clientRequest.UserPurchaseId))
            {
                return BadRequest("Request body missing PurchaseId. ex: {\"PurchaseId\": \"...\"}");
            }

            bool includeJson = false;
            if (Request.Headers.ContainsKey("User-Agent"))
            {
                string userAgent = Request.Headers["User-Agent"];
                if (!string.IsNullOrEmpty(userAgent) && userAgent.Equals("Microsoft.StoreServicesClientSample"))
                {
                    //  此调用来自与此示例绑定的客户端示例，因此在响应体中包含添加的 JSON，以便客户端可以使用这些值来更新 UI。
                    includeJson = true;
                }
            }

            var response = new StringBuilder("");

            var recurrenceRequest = new RecurrenceQueryRequest
            {
                UserPurchaseId = clientRequest.UserPurchaseId
            };

            if (!string.IsNullOrEmpty(clientRequest.Sbx))
            {
                recurrenceRequest.SandboxId = clientRequest.Sbx;
            }

            var recurrenceResults = new RecurrenceQueryResponse();
            using (var storeClient = _storeServicesClientFactory.CreateClient())
            {
                recurrenceResults = await storeClient.RecurrenceQueryAsync(recurrenceRequest);
            }

            try
            {
                response.Append(
                    "| ProductId    | Renew | State     | Start Date                    | Expire Date (With Grace)      |\n" +
                    "|--------------------------------------------------------------------------------------------------|\n");

                foreach (var item in recurrenceResults.Items)
                {
                    response.AppendFormat("| {0,-12} | {1,-5} | {2,-9} | {3,-29} | {4,-29} |\n",
                                            item.ProductId,
                                            item.AutoRenew,
                                            item.RecurrenceState,
                                            item.StartTime.ToString(),
                                            item.ExpirationTimeWithGrace);
                }
            }
            catch (Exception e)
            {
                response.Append(e.Message);
            }
            if (includeJson)
            {
                response.Append("RawResponse: ");
                response.Append(JsonConvert.SerializeObject(recurrenceResults));
            }

            var finalResponse = response.ToString();
            return new OkObjectResult(finalResponse);
        }

        /// <summary>
        /// 根据提供的用户购买ID返回任何订阅信息 可改变状态 https://learn.microsoft.com/zh-cn/gaming/gdk/docs/store/commerce/service-to-service/microsoft-store-apis/xstore-v8-recurrence-change
        /// </summary>
        /// <param name="clientRequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<string>> RecurrenceChange([FromBody] ClientRecurrenceChangeRequest clientRequest)
        {
            //  Check that we have a properly formatted request body
            if (string.IsNullOrEmpty(clientRequest.UserPurchaseId))
            {
                return BadRequest("Request body missing PurchaseId. ex: {\"PurchaseId\": \"...\"}");
            }
            else if (string.IsNullOrEmpty(clientRequest.RecurrenceId))
            {
                return BadRequest("Request body missing RecurrenceId. ex: {\"RecurrenceId\": \"...\"}");
            }
            else if (string.IsNullOrEmpty(clientRequest.ChangeType))
            {
                return BadRequest("Request body missing RecurrenceId. ex: {\"ChangeType\": \"...\"}, Cancel, Extend, Refund, ToggleAutoRenew");
            }

            bool includeJson = false;
            if (Request.Headers.ContainsKey("User-Agent"))
            {
                string userAgent = Request.Headers["User-Agent"];
                if (!string.IsNullOrEmpty(userAgent) && userAgent.Equals("Microsoft.StoreServicesClientSample"))
                {
                    //  This call is from the Client sample that is tied to this sample, so include the added JSON
                    //  in the response body so that it can use those values to update the UI.
                    includeJson = true;
                }
            }

            var response = new StringBuilder("");

            var recurrenceRequest = new RecurrenceChangeRequest
            {
                UserPurchaseId = clientRequest.UserPurchaseId,
                ChangeType = clientRequest.ChangeType,
                RecurrenceId = clientRequest.RecurrenceId
            };

            if (!string.IsNullOrEmpty(clientRequest.Sbx))
            {
                recurrenceRequest.SandboxId = clientRequest.Sbx;
            }

            if (clientRequest.ChangeType == RecurrenceChangeType.Extend.ToString())
            {
                recurrenceRequest.ExtensionTimeInDays = clientRequest.ExtensionTime;
            }

            var recurrenceResult = new RecurrenceChangeResponse();
            using (var storeClient = _storeServicesClientFactory.CreateClient())
            {
                recurrenceResult = await storeClient.RecurrenceChangeAsync(recurrenceRequest);
            }

            try
            {
                response.Append(
                    "| ProductId    | Renew | State     | Start Date                    | Expire Date (With Grace)      |\n" +
                    "|--------------------------------------------------------------------------------------------------|\n");

                response.AppendFormat("| {0,-12} | {1,-5} | {2,-9} | {3,-29} | {4,-29} |\n",
                                      recurrenceResult.ProductId,
                                      recurrenceResult.AutoRenew,
                                      recurrenceResult.RecurrenceState,
                                      recurrenceResult.StartTime.ToString(),
                                      recurrenceResult.ExpirationTimeWithGrace);
            }
            catch (Exception e)
            {
                response.Append(e.Message);
            }

            if (includeJson)
            {
                response.Append("RawResponse: ");
                response.Append(JsonConvert.SerializeObject(recurrenceResult));
            }

            var finalResponse = response.ToString();
            return new OkObjectResult(finalResponse);
        }

        /// <summary>
        /// TODO：此端点在部署的服务中不会是一个真正的端点。它会被其他内容替换。
        /// 一个每天运行一次且无需客户端请求即可控制的函数
        ///
        /// 运行任务以遍历并核对扣回队列中的所有项目
        /// 如果我们已经消费的商品有退款，那么
        /// 需要从用户账户余额中撤销或移除项目，在我们的服务器上进行
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<string>> RunClawbackValidation()
        {
            InitializeLoggingCv();
            var response = new StringBuilder("Running Clawback Reconciliation Task...  \n");

            var clawManager = new ClawbackV1Manager(_config, _storeServicesClientFactory, _logger);
            response.Append(await clawManager.RunClawbackReconciliationAsync(_cV));

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// TODO: 此端点不应作为已部署服务中的端点。它应是
        /// 一个每日自动运行的任务，无需客户端请求触发。
        ///
        /// 执行该任务以遍历并核对所有“回撤队列”中的条目，
        /// 检查是否存在针对已消费商品的退款情况。
        /// 若存在，则需撤销/从用户账户余额中扣除相应商品。
        /// </summary>
        /// <returns></returns>

        [HttpGet]
        public ActionResult<string> ViewClawbackQueue()
        {
            InitializeLoggingCv();
            var response = new StringBuilder("Clawback items being tracked:\n");

            var clawManager = new ClawbackV1Manager(_config, _storeServicesClientFactory, _logger);

            var clawbackQueue = clawManager.GetClawbackQueue(_cV);
            foreach (var clawbackQueueItem in clawbackQueue)
            {
                response.AppendFormat("User {0}'s last transaction on {1}, {2}\n",
                                      clawbackQueueItem.DbKey,
                                      clawbackQueueItem.ConsumeDate,
                                      clawbackQueueItem.UserPurchaseId);
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }


        /// <summary>
        /// 注意：这仅是一个测试 API，不应部署到生产环境
        /// 处于构建状态的所有 Clawback 操作项的完整列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult<string> ViewReconciledTransactions()
        {
            InitializeLoggingCv();
            var response = new StringBuilder("Getting CompletedTransactionItems that have been reconciled:\n");

            var reconciledTransactions = new List<CompletedConsumeTransaction>();
            using (var dbContext = ServerDBController.CreateDbContext(_config, _cV, _logger))
            {
                reconciledTransactions = dbContext.CompletedConsumeTransactions.Where(
                    b => b.TransactionStatus == CompletedConsumeTransactionState.Reconciled
                    ).ToList();
            }

            if (reconciledTransactions.Count > 0)
            {
                response.Append(FormatResponseForCompletedTransactions(reconciledTransactions));
            }
            else
            {
                response.Append("No CompletedTransactionItems found");
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// 注意：此API仅用于测试，不应包含在生产部署中
        /// 建筑状态下所有Clawback操作项的完整列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult<string> ViewCompletedTransactions()
        {
            InitializeLoggingCv();
            var response = new StringBuilder("Getting CompletedTransactionItems:\n");

            var transactions = new List<CompletedConsumeTransaction>();
            using (var dbContext = ServerDBController.CreateDbContext(_config, _cV, _logger))
            {
                transactions = dbContext.CompletedConsumeTransactions.ToList();
            }

            if (transactions.Count > 0)
            {
                response.Append(FormatResponseForCompletedTransactions(transactions));
            }
            else
            {
                response.Append("No CompletedTransactionItems found");
            }

            FinalizeLoggingCv();
            return new OkObjectResult(response.ToString());
        }

        /// <summary>
        /// 实用函数，用于帮助格式化响应，以便将其发送回客户端
        /// test endpoints
        /// </summary>
        /// <param name="actionItems"></param>
        /// <returns></returns>
        private static string FormatResponseForCompletedTransactions(List<CompletedConsumeTransaction> transactions)
        {
            var response = new StringBuilder("\n");
            response.Append(
                    "| TrackingId                           | Status       | ProductId    | Quantity | UserId           | Consumed Date                 | OrderId                              | OrderLineItemId                      |\n" +
                    "|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n");

            foreach (var transaction in transactions)
            {
                response.AppendFormat("| {0,-36} | {1,-12} | {2,-12} | {3,-8} | {4,-16} | {5,-29} | {6,-36} | {7,-36} |\n",
                                      transaction.TrackingId,
                                      transaction.TransactionStatus,
                                      transaction.ProductId,
                                      transaction.QuantityConsumed,
                                      transaction.UserId,
                                      transaction.ConsumeDate,
                                      transaction.OrderId,
                                      transaction.OrderLineItemId);

                response.AppendFormat(
                   "|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\n");
            }

            response.AppendFormat("\n");

            return response.ToString();
        }
    }
}