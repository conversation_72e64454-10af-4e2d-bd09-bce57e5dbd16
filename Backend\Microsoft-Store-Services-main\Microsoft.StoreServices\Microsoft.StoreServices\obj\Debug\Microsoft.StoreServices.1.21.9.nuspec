﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Microsoft.StoreServices</id>
    <version>1.21.9</version>
    <authors>Xbox Advanced Technology Group</authors>
    <icon>Logo.png</icon>
    <description>This client library enables working with the Microsoft Store Services to manage and validate user purchases within the Microsoft Store.  This library specifically has functionality to help request the needed Azure Active Directory access tokens and call the store services with this authorization.  More information can be found in the following articles:
          Monetization, engagement, and Store services - https://docs.microsoft.com/en-us/windows/uwp/monetize/
          Manage product entitlements from a service - https://docs.microsoft.com/en-us/windows/uwp/monetize/view-and-grant-products-from-a-service</description>
    <copyright>Copyright (C) Microsoft Corporation. All rights reserved.</copyright>
    <tags>Microsoft Store Services Microsoft Xbox</tags>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net7.0">
        <dependency id="Azure.Storage.Queues" version="12.17.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.AspNetCore.Mvc.NewtonsoftJson" version="7.0.18" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore" version="7.0.18" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.InMemory" version="7.0.18" exclude="Build,Analyzers" />
        <dependency id="Microsoft.EntityFrameworkCore.SqlServer" version="7.0.18" exclude="Build,Analyzers" />
        <dependency id="Newtonsoft.Json" version="13.0.3" exclude="Build,Analyzers" />
        <dependency id="jose-jwt" version="5.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\旧电脑\MicrsoftStore\Backend\Microsoft-Store-Services-main\Microsoft.StoreServices\Microsoft.StoreServices\bin\Debug\net7.0\Microsoft.StoreServices.runtimeconfig.json" target="lib\net7.0\Microsoft.StoreServices.runtimeconfig.json" />
    <file src="D:\旧电脑\MicrsoftStore\Backend\Microsoft-Store-Services-main\Microsoft.StoreServices\Microsoft.StoreServices\bin\Debug\net7.0\Microsoft.StoreServices.dll" target="lib\net7.0\Microsoft.StoreServices.dll" />
    <file src="D:\旧电脑\MicrsoftStore\Backend\Microsoft-Store-Services-main\Microsoft.StoreServices\Microsoft.StoreServices\Media\Logo.png" target="Logo.png" />
  </files>
</package>