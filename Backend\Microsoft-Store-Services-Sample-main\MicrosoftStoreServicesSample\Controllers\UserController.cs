﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.StoreServices;
using MicrosoftStoreServicesSample.PersistentDB;
using System.Threading.Tasks;
using System;
using Microsoft.EntityFrameworkCore;
using MicrosoftStoreServicesSample.Models;
using System.Linq;
using System.Collections.Generic;

namespace MicrosoftStoreServicesSample.Controllers
{

    [ApiController]
    [Route("[controller]/[action]")]
    public class UserController: ServiceControllerBase
    {

        private readonly IConfiguration _config;
        private readonly ServerDBContext _serverDBContext;

        public UserController(IConfiguration config,
                                     ServerDBContext serverDBContext,
                                     IStoreServicesClientFactory storeServicesClientFactory,
                                     ILogger<UserController> logger) :
            base(storeServicesClientFactory, logger)
        {
            _config = config;
            _serverDBContext = serverDBContext;
        }


        /// <summary>
        /// 注册用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        /// 
        [HttpPost]
        public async Task<IActionResult> AddUser(string userId) 
        {
            InitializeLoggingCv();

            if (string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogError(_cV.Value, "用户ID不能为空");
                FinalizeLoggingCv();
                return BadRequest("用户ID不能为空");
            }

            // 检查用户是否已存在
            var existingUser = await _serverDBContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (existingUser != null)
            {
                _logger.LogWarning(_cV.Value, $"用户ID {userId} 已存在");
                FinalizeLoggingCv();
                return BadRequest($"用户ID {userId} 已存在");
            }

            try
            {
                // 创建新用户
                var newUser = new User
                {
                    UserId = userId,
                    CreateTime = DateTime.Now,
                    LastUpdateTime = DateTime.Now
                };

                _serverDBContext.Users.Add(newUser);
                await _serverDBContext.SaveChangesAsync();

                _logger.LogInformation(_cV.Value, $"用户 {userId} 创建成功");
                FinalizeLoggingCv();
                return Ok(new { userId = newUser.UserId, result = true, message = "用户创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, $"创建用户 {userId} 失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"创建用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetUser(string userId)
        {
            InitializeLoggingCv();

            if (string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogError(_cV.Value, "用户ID不能为空");
                FinalizeLoggingCv();
                return BadRequest("用户ID不能为空");
            }

            try
            {
                var user = await _serverDBContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    _logger.LogWarning(_cV.Value, $"未找到用户ID: {userId}");
                    FinalizeLoggingCv();
                    return NotFound($"未找到用户ID: {userId}");
                }

                _logger.LogInformation(_cV.Value, $"获取用户 {userId} 信息成功");
                FinalizeLoggingCv();
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, $"获取用户 {userId} 信息失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"获取用户信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="userId">用户ID筛选条件（可选）</param>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <returns>用户列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetUsers(string userId = "", int page = 1, int limit = 10)
        {
            InitializeLoggingCv();

            try
            {
                // 构建查询
                IQueryable<User> query = _serverDBContext.Users;

                // 应用筛选条件
                if (!string.IsNullOrWhiteSpace(userId))
                {
                    query = query.Where(u => u.UserId.Contains(userId));
                }

                // 获取总记录数
                int total = await query.CountAsync();

                // 应用分页
                var users = await query
                    .OrderByDescending(u => u.LastUpdateTime)
                    .Skip((page - 1) * limit)
                    .Take(limit)
                    .ToListAsync();

                // 构建响应数据
                var result = new
                {
                    total,
                    items = users,
                    page,
                    limit
                };

                _logger.LogInformation(_cV.Value, $"获取用户列表成功，总数: {total}");
                FinalizeLoggingCv();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, "获取用户列表失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"获取用户列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>操作结果</returns>
        [HttpPut]
        public async Task<IActionResult> UpdateUser([FromBody] User user)
        {
            InitializeLoggingCv();

            if (user == null || string.IsNullOrWhiteSpace(user.UserId))
            {
                _logger.LogError(_cV.Value, "用户信息不完整");
                FinalizeLoggingCv();
                return BadRequest("用户信息不完整");
            }

            try
            {
                var existingUser = await _serverDBContext.Users.FirstOrDefaultAsync(u => u.UserId == user.UserId);
                if (existingUser == null)
                {
                    _logger.LogWarning(_cV.Value, $"未找到用户ID: {user.UserId}");
                    FinalizeLoggingCv();
                    return NotFound($"未找到用户ID: {user.UserId}");
                }

                // 更新用户信息
                existingUser.UserPurchaseId = user.UserPurchaseId;
                existingUser.UserCollectionsId = user.UserCollectionsId;
                existingUser.LastUpdateTime = DateTime.Now;

                _serverDBContext.Users.Update(existingUser);
                await _serverDBContext.SaveChangesAsync();

                _logger.LogInformation(_cV.Value, $"更新用户 {user.UserId} 信息成功");
                FinalizeLoggingCv();
                return Ok(new { userId = existingUser.UserId, result = true, message = "用户信息更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, $"更新用户 {user.UserId} 信息失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"更新用户信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete]
        public async Task<IActionResult> DeleteUser(string userId)
        {
            InitializeLoggingCv();

            if (string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogError(_cV.Value, "用户ID不能为空");
                FinalizeLoggingCv();
                return BadRequest("用户ID不能为空");
            }

            try
            {
                var user = await _serverDBContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    _logger.LogWarning(_cV.Value, $"未找到用户ID: {userId}");
                    FinalizeLoggingCv();
                    return NotFound($"未找到用户ID: {userId}");
                }

                // 检查用户是否有相关订单
                var hasOrders = await _serverDBContext.Orders.AnyAsync(o => o.UserId == userId);
                if (hasOrders)
                {
                    _logger.LogWarning(_cV.Value, $"用户 {userId} 有相关订单，无法删除");
                    FinalizeLoggingCv();
                    return BadRequest($"用户 {userId} 有相关订单，无法删除");
                }

                _serverDBContext.Users.Remove(user);
                await _serverDBContext.SaveChangesAsync();

                _logger.LogInformation(_cV.Value, $"删除用户 {userId} 成功");
                FinalizeLoggingCv();
                return Ok(new { userId, result = true, message = "用户删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, $"删除用户 {userId} 失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"删除用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户访问令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>访问令牌信息</returns>
        [HttpGet]
        public async Task<IActionResult> GetUserAccessTokens(string userId)
        {
            InitializeLoggingCv();

            if (string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogError(_cV.Value, "用户ID不能为空");
                FinalizeLoggingCv();
                return BadRequest("用户ID不能为空");
            }

            try
            {
                var user = await _serverDBContext.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null)
                {
                    _logger.LogWarning(_cV.Value, $"未找到用户ID: {userId}");
                    FinalizeLoggingCv();
                    return NotFound($"未找到用户ID: {userId}");
                }

                var tokens = await GetAccessTokens();
                var response = new
                {
                    userId,
                    accessTokens = tokens
                };

                _logger.LogInformation(_cV.Value, $"获取用户 {userId} 访问令牌成功");
                FinalizeLoggingCv();
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(_cV.Value, ex, $"获取用户 {userId} 访问令牌失败");
                FinalizeLoggingCv();
                return StatusCode(500, $"获取用户访问令牌失败: {ex.Message}");
            }
        }
    }
}
