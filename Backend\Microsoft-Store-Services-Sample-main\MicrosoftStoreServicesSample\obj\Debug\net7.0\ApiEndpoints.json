[{"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ClawbackV1Query", "RelativePath": "Clawback/ClawbackV1Query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "MicrosoftStoreServicesSample.ClientPurchaseId", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ClawbackV2Peek", "RelativePath": "Clawback/ClawbackV2Peek", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ClawbackV2Query", "RelativePath": "Clawback/ClawbackV2Query", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "sandboxFilter", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "RunClawbackV2Validation", "RelativePath": "Clawback/RunClawbackV2Validation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "RunClawbackValidation", "RelativePath": "Clawback/RunClawbackValidation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ViewClawbackQueue", "RelativePath": "Clawback/ViewClawbackQueue", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ViewCompletedTransactions", "RelativePath": "Clawback/ViewCompletedTransactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.ClawbackController", "Method": "ViewReconciledTransactions", "RelativePath": "Clawback/ViewReconciledTransactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "AddPendingConsume", "RelativePath": "Collections/AddPendingConsume", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "Microsoft.StoreServices.ClientConsumeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "Consume", "RelativePath": "Collections/Consume", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "Microsoft.StoreServices.ClientConsumeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "QueryV8", "RelativePath": "Collections/QueryV8", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "Microsoft.StoreServices.ClientCollectionsQueryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "QueryV9", "RelativePath": "Collections/QueryV9", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "Microsoft.StoreServices.ClientCollectionsQueryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "RetrieveAccessTokens", "RelativePath": "Collections/RetrieveAccessTokens", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "RetryPendingConsumes", "RelativePath": "Collections/RetryPendingConsumes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "ViewPendingConsumes", "RelativePath": "Collections/ViewPendingConsumes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.CollectionsController", "Method": "ViewUserBalances", "RelativePath": "Collections/ViewUserBalances", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.OrderController", "Method": "AddOrder", "RelativePath": "Order/AddOrder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "order", "Type": "MicrosoftStoreServicesSample.Models.Order", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.OrderController", "Method": "VerifyOrder", "RelativePath": "Order/VerifyOrder", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "AccessTokens", "RelativePath": "Purchase/AccessTokens", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "RecurrenceC<PERSON>e", "RelativePath": "Purchase/RecurrenceChange", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "MicrosoftStoreServicesSample.ClientRecurrenceChangeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "Recurrence<PERSON><PERSON><PERSON>", "RelativePath": "Purchase/RecurrenceQuery", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientRequest", "Type": "MicrosoftStoreServicesSample.ClientPurchaseId", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "RunClawbackValidation", "RelativePath": "Purchase/RunClawbackValidation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "ViewClawbackQueue", "RelativePath": "Purchase/ViewClawbackQueue", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "ViewCompletedTransactions", "RelativePath": "Purchase/ViewCompletedTransactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.PurchaseController", "Method": "ViewReconciledTransactions", "RelativePath": "Purchase/ViewReconciledTransactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "AddUser", "RelativePath": "User/AddUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "User/DeleteUser", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "GetUser", "RelativePath": "User/GetUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "GetUserAccessTokens", "RelativePath": "User/GetUserAccessTokens", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "GetUsers", "RelativePath": "User/GetUsers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "MicrosoftStoreServicesSample.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "User/UpdateUser", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "MicrosoftStoreServicesSample.Models.User", "IsRequired": true}], "ReturnTypes": []}]