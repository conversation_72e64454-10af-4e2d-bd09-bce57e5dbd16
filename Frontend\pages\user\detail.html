<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>用户详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- 引入 layui.css -->
    <link href="//unpkg.com/layui@2.11.5/dist/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" href="../../css/main.css" media="all">
    <style>
        .user-detail-item {
            margin-bottom: 15px;
        }
        .user-detail-item .layui-form-label {
            text-align: left;
            padding-left: 0;
            width: 120px;
        }
        .user-detail-item .layui-input-block {
            margin-left: 120px;
        }
        .user-detail-title {
            font-weight: bold;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .layui-elem-quote {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <blockquote class="layui-elem-quote">用户详细信息</blockquote>
                <div class="user-detail-container">
                    <div class="user-detail-item">
                        <label class="layui-form-label">用户ID：</label>
                        <div class="layui-input-block">
                            <div class="layui-form-mid" id="userId"></div>
                        </div>
                    </div>
                    
                    <div class="user-detail-item">
                        <label class="layui-form-label">购买ID：</label>
                        <div class="layui-input-block">
                            <div class="layui-form-mid" id="userPurchaseId"></div>
                        </div>
                    </div>
                    
                    <div class="user-detail-item">
                        <label class="layui-form-label">收藏ID：</label>
                        <div class="layui-input-block">
                            <div class="layui-form-mid" id="userCollectionsId"></div>
                        </div>
                    </div>
                    
                    <div class="user-detail-item">
                        <label class="layui-form-label">创建时间：</label>
                        <div class="layui-input-block">
                            <div class="layui-form-mid" id="createTime"></div>
                        </div>
                    </div>
                    
                    <div class="user-detail-item">
                        <label class="layui-form-label">更新时间：</label>
                        <div class="layui-input-block">
                            <div class="layui-form-mid" id="lastUpdateTime"></div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-primary" id="closeBtn">关闭</button>
                        <button class="layui-btn layui-btn-normal" id="editBtn">编辑用户</button>
                        <button class="layui-btn layui-btn-warm" id="tokenBtn">获取令牌</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入 layui.js -->
    <script src="//unpkg.com/layui@2.11.5/dist/layui.js"></script>
    <script>
        layui.config({
            base: '../../js/'
        }).extend({
            userApi: 'api'
        }).use(['layer', 'userApi'], function() {
            var layer = layui.layer;
            var userApi = layui.userApi;
            var $ = layui.jquery;
            
            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }
            
            // 加载用户数据
            var userId = getUrlParam('userId');
            if (userId) {
                userApi.getUser(userId, function(err, res) {
                    if (err) {
                        layer.msg(err.message || '获取用户信息失败', {icon: 2});
                        return;
                    }
                    
                    // 显示用户信息
                    $('#userId').text(res.userId || '-');
                    $('#userPurchaseId').text(res.userPurchaseId || '-');
                    $('#userCollectionsId').text(res.userCollectionsId || '-');
                    $('#createTime').text(res.createTime ? new Date(res.createTime).toLocaleString() : '-');
                    $('#lastUpdateTime').text(res.lastUpdateTime ? new Date(res.lastUpdateTime).toLocaleString() : '-');
                });
            }
            
            // 关闭按钮事件
            $('#closeBtn').on('click', function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
            
            // 编辑按钮事件
            $('#editBtn').on('click', function() {
                parent.layer.open({
                    type: 2,
                    title: '编辑用户',
                    content: 'edit.html?userId=' + userId,
                    area: ['800px', '600px'],
                    maxmin: true,
                    end: function() {
                        // 刷新页面
                        window.location.reload();
                    }
                });
            });
            
            // 获取令牌按钮事件
            $('#tokenBtn').on('click', function() {
                userApi.getUserAccessTokens(userId, function(err, res) {
                    if (err) {
                        layer.msg(err.message || '获取令牌失败', {icon: 2});
                        return;
                    }
                    
                    var tokenHtml = '<div class="layui-text">';
                    tokenHtml += '<p><strong>用户ID:</strong> ' + res.userId + '</p>';
                    tokenHtml += '<p><strong>访问令牌:</strong></p>';
                    tokenHtml += '<textarea class="layui-textarea" style="height: 150px;">' + JSON.stringify(res.accessTokens, null, 2) + '</textarea>';
                    tokenHtml += '</div>';
                    
                    layer.open({
                        type: 1,
                        title: '用户访问令牌',
                        content: tokenHtml,
                        area: ['600px', '400px']
                    });
                });
            });
        });
    </script>
</body>
</html> 