layui.use(['element', 'layer', 'jquery'], function() {
    var element = layui.element;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 全局API配置
    window.API_CONFIG = {
        base_url: 'https://127.0.0.1:53804', // API基础URL，根据实际情况修改
        version: '1.0.0'
    };
    
    // 暴露接口给外部访问 (兼容之前的代码)
    window.api = {
        config: window.API_CONFIG
    };
    
    // 左侧菜单点击事件
    $('.layui-nav-item a').on('click', function() {
        var url = $(this).data('url');
        var id = $(this).data('id');
        var title = $(this).text();
        
        // 判断选项卡是否已存在
        if ($('.layui-tab-title li[lay-id="' + id + '"]').length > 0) {
            // 切换到已有选项卡
            element.tabChange('tab', id);
        } else {
            // 新建选项卡
            element.tabAdd('tab', {
                title: title,
                content: '<iframe src="' + url + '" frameborder="0" class="layadmin-iframe"></iframe>',
                id: id
            });
            // 切换到新选项卡
            element.tabChange('tab', id);
        }
    });
    
    // 请求拦截器
    var _ajax = $.ajax;
    $.ajax = function(options) {
        var originalSuccess = options.success;
        var originalError = options.error;
        
        var newOptions = $.extend({}, options, {
            url: options.url.indexOf('http') === 0 ? options.url : window.API_CONFIG.base_url + options.url,
            success: function(res) {
                // 处理成功回调
                if (originalSuccess) {
                    originalSuccess(res);
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                // 处理错误
                if (xhr.status === 401) {
                    layer.msg('登录已过期，请重新登录', {icon: 2});
                } else if (xhr.status === 403) {
                    layer.msg('没有权限执行此操作', {icon: 2});
                } else {
                    layer.msg('请求失败：' + (xhr.responseJSON ? xhr.responseJSON.message : '未知错误'), {icon: 2});
                }
                
                if (originalError) {
                    originalError(xhr, textStatus, errorThrown);
                }
            }
        });
        
        return _ajax(newOptions);
    };
    
    // 初始化
    (function() {
        // 触发第一个菜单的点击事件
        $('.layui-nav-item.layui-this a').trigger('click');
    })();
}); 