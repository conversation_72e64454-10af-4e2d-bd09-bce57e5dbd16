﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicrosoftStoreServicesSample.Models
{
    public class User
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string UserId { get; set; }
        public string UserPurchaseId { get; set; }
        public string UserCollectionsId { get; set; }
        public bool IsVip { get; set; } = false;
        public DateTime VipExpTime { get; set; } = DateTime.MinValue;
        public DateTime CreateTime { get; set; } = DateTime.Now; 
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }
}
