//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("d541f79d-2f04-47de-872e-00c9a8ea1afd")]
[assembly: System.Reflection.AssemblyCompanyAttribute("Microsoft")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright (C) Microsoft Corporation. All rights reserved.")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("Sample Service to demonstrate calling the Microsoft Store Services and authentica" +
    "ting with them.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("********")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.21.09")]
[assembly: System.Reflection.AssemblyProductAttribute("MicrosoftStoreServicesSample")]
[assembly: System.Reflection.AssemblyTitleAttribute("MicrosoftStoreServicesSample")]
[assembly: System.Reflection.AssemblyVersionAttribute("********")]

// 由 MSBuild WriteCodeFragment 类生成。

