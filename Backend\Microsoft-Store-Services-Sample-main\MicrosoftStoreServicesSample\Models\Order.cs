﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MicrosoftStoreServicesSample.Models
{
    public class Order
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public string Name { get; set; }

        public string OrderId { get; set; }

        /// <summary>
        /// 订单标识值：pending、succeed 和 refuse、failed 和 unknown。
        /// </summary>
        public string Status { get; set; } = "pending";

        public double Price { get; set; }

        public string UserId { get; set; }

        /// <summary>
        /// 可用于将密钥导入到定期管理 API 的项目 ID	主要用于订阅改变状态/退款/取消订阅等操作。 若要使用此字段。
        /// </summary>
        public string RecurrenceId { get; set; }

        public string StoreId { get; set; }

        /// <summary>
        ///
        //  当前支持以下值： Application、 Game、 Consumable、 UnmanagedConsumable 和 Durable。
        // Application 非游戏类应用，例如工具、效率类软件等。
        // Game 游戏类应用，例如休闲游戏、角色扮演游戏等。
        // Consumable 消耗品，例如游戏内货币、道具等。
        // UnmanagedConsumable 非托管消耗品，例如一次性使用的商品。 
        // Durable 持久性商品，例如永久解锁的功能、扩展包等。
        /// </summary>
        public string ProductKind { get; set; }

        /// <summary>
        /// 加载项 代码名称
        /// </summary>
        public string InAppOfferToken { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否自动续订
        /// </summary>
        public bool AutoRenew { get; set; }

        /// <summary>
        /// 服务过期时间
        /// </summary>
        public DateTimeOffset ExpirationTime { get; set; }

        /// <summary>
        /// 宽限期服务过期时间，
        /// 当自动续订在 ExpirationTime 失败时，用户宽限期将结束的 UTC 日期和时间。 在 Grace 期间，用户仍应具有访问权限，并被视为有效的订阅者，但通知他们需要修复其自动续订付款。	
        /// </summary>
        public DateTimeOffset ExpirationTimeWithGrace { get; set; }


    }
}
