﻿//-----------------------------------------------------------------------------
// AccessToken.cs
//
// Xbox Advanced Technology Group (ATG)
// Copyright (c) Microsoft Corporation.  All rights reserved.
// Licensed under the MIT License. See License file under the project root for
// license information.
//-----------------------------------------------------------------------------

using Newtonsoft.Json;
using System;

namespace Microsoft.StoreServices
{
    /// <summary>
    /// Access tokens are generated by your service and either used as the Authorization
    /// header for calls to the Microsoft.Store services, or sent to the client to
    /// generate a UserStoreId.  The specified Audience of the access token will
    /// determine what it is used for:
    /// Service Access Token -     https://onestore.microsoft.com - Used as the bearer token
    ///                            in the Authorization header of service-to-service calls.
    /// Purchase Access Token -    https://onestore.microsoft.com/b2b/keys/create/purchase
    ///                            Sent to the client to generate a UserPurchaseId
    /// Collections Access Token - https://onestore.microsoft.com/b2b/keys/create/collections
    ///                            Sent to the client to generate a UserCollectionsId
    /// </summary>
    public class AccessToken
    {
        private uint expiresIn;

        /// <summary>
        /// Lifetime of the token in seconds from when it was created
        /// </summary>
        [JsonProperty("expires_in")]
        public uint ExpiresIn
        {
            get { return expiresIn; }

            //  The Azure AAD 2.0 URI only includes the expires_in time value so we need to
            //  generate the ExpiresOn value based off of it and when this was created.
            set
            {
                expiresIn = value;
                ExpiresOn = DateTimeOffset.Now.AddSeconds(value);
            }
        }

        /// <summary>
        /// Audience URI tied to the token which determines its type and which Microsoft Store Service it can be used with.
        /// With the Azure AD URI 1.0 this was returned in the result, with 2.0 it is not and needs to be set manually.
        /// </summary>
        [JsonProperty("resource")]
        public string Audience { get; set; }

        /// <summary>
        /// Actual token to be used in calling the services or obtaining User Store Ids
        /// </summary>
        [JsonProperty("access_token")]
        public string Token { get; set; }

        /// <summary>
        /// The UTC date and time when the Access Token expires
        /// </summary>
        public DateTimeOffset ExpiresOn { get; set; }
    }

    /// <summary>
    /// Audience URI string values for each of the Access Token types used for Microsoft Store Services auth
    /// </summary>
    public class AccessTokenAudienceTypes
    {
        /// <summary>
        /// Service access tokens identify the AAD tenant and your service.
        /// This token is used in the Authorization header of all calls to the Microsoft Store Services.
        /// </summary>
        public const string Service = "https://onestore.microsoft.com";

        /// <summary>
        /// Collections access tokens are passed to the client and used to generate UserCollectionsIds.
        /// </summary>
        public const string Collections = "https://onestore.microsoft.com/b2b/keys/create/collections";

        /// <summary>
        /// Purchase access tokens are passed to your client app and used to generate UserPurchaseIds.
        /// </summary>
        public const string Purchase = "https://onestore.microsoft.com/b2b/keys/create/purchase";
    }
}