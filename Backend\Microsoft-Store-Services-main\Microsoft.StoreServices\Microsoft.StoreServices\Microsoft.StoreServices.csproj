<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
      <Authors>Xbox Advanced Technology Group</Authors>
      <PackageId>Microsoft.StoreServices</PackageId>
      <Company>Microsoft</Company>
      <Version>1.21.09</Version>
      <PackageTags>Microsoft Store Services;Microsoft;Xbox</PackageTags>
      <Description>This client library enables working with the Microsoft Store Services to manage and validate user purchases within the Microsoft Store.  This library specifically has functionality to help request the needed Azure Active Directory access tokens and call the store services with this authorization.  More information can be found in the following articles:
          Monetization, engagement, and Store services - https://docs.microsoft.com/en-us/windows/uwp/monetize/
          Manage product entitlements from a service - https://docs.microsoft.com/en-us/windows/uwp/monetize/view-and-grant-products-from-a-service
</Description>
      <Copyright>Copyright (C) Microsoft Corporation. All rights reserved.</Copyright>
      <PackageIcon>Logo.png</PackageIcon>
      <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
  </PropertyGroup>

    <ItemGroup>
        <Content Include="Media\Logo.png">
            <Pack>True</Pack>
            <PackagePath></PackagePath>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Azure.Storage.Queues" Version="12.17.1" />
      <PackageReference Include="jose-jwt" Version="5.0.0" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="7.0.18" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.18" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.18">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.18" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.18" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.18">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

</Project>
