﻿//-----------------------------------------------------------------------------
// RecurrenceChangeResponse.cs
//
// Xbox Advanced Technology Group (ATG)
// Copyright (C) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License file under the project root for
// license information.
//-----------------------------------------------------------------------------

namespace Microsoft.StoreServices
{
    /// <summary>
    /// JSON response body from a change request to a subscription
    /// </summary>
    public class RecurrenceChangeResponse : RecurrenceItem
    {
        //  Recurrence Change returns just one item so no additional
        //  properties are needed.
    }
}