/**
 * 用户管理API接口
 */
layui.define(['jquery', 'layer'], function(exports) {
    var $ = layui.jquery;
    var layer = layui.layer;
    
    // 获取API配置
    var getApiConfig = function() {
        // 优先使用全局配置，如果不存在则使用父窗口的配置，最后使用默认值
        if (window.API_CONFIG) {
            return window.API_CONFIG;
        } else if (parent && parent.API_CONFIG) {
            return parent.API_CONFIG;
        } else {
            return { base_url: 'https://127.0.0.1:53804' };
        }
    };
    
    var userApi = {
        /**
         * 获取API配置
         * @returns {Object} API配置对象
         */
        getApiConfig: getApiConfig,
        
        /**
         * 添加用户
         * @param {String} userId 用户ID
         * @param {Function} callback 回调函数
         */
        addUser: function(userId, callback) {
            var apiConfig = getApiConfig();
            $.ajax({
                url: apiConfig.base_url + '/User/AddUser',
                type: 'POST',
                data: { userId: userId },
                success: function(res) {
                    callback && callback(null, res);
                },
                error: function(xhr) {
                    callback && callback(xhr.responseJSON || { message: '添加用户失败' });
                }
            });
        },
        
        /**
         * 获取用户信息
         * @param {String} userId 用户ID
         * @param {Function} callback 回调函数
         */
        getUser: function(userId, callback) {
            var apiConfig = getApiConfig();
            $.ajax({
                url: apiConfig.base_url + '/User/GetUser',
                type: 'GET',
                data: { userId: userId },
                success: function(res) {
                    callback && callback(null, res);
                },
                error: function(xhr) {
                    callback && callback(xhr.responseJSON || { message: '获取用户信息失败' });
                }
            });
        },
        
        /**
         * 更新用户信息
         * @param {Object} userData 用户数据
         * @param {Function} callback 回调函数
         */
        updateUser: function(userData, callback) {
            var apiConfig = getApiConfig();
            $.ajax({
                url: apiConfig.base_url + '/User/UpdateUser',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(userData),
                success: function(res) {
                    callback && callback(null, res);
                },
                error: function(xhr) {
                    callback && callback(xhr.responseJSON || { message: '更新用户失败' });
                }
            });
        },
        
        /**
         * 删除用户
         * @param {String} userId 用户ID
         * @param {Function} callback 回调函数
         */
        deleteUser: function(userId, callback) {
            var apiConfig = getApiConfig();
            $.ajax({
                url: apiConfig.base_url + '/User/DeleteUser',
                type: 'DELETE',
                data: { userId: userId },
                success: function(res) {
                    callback && callback(null, res);
                },
                error: function(xhr) {
                    callback && callback(xhr.responseJSON || { message: '删除用户失败' });
                }
            });
        },
        
        /**
         * 获取用户访问令牌
         * @param {String} userId 用户ID
         * @param {Function} callback 回调函数
         */
        getUserAccessTokens: function(userId, callback) {
            var apiConfig = getApiConfig();
            $.ajax({
                url: apiConfig.base_url + '/User/GetUserAccessTokens',
                type: 'GET',
                data: { userId: userId },
                success: function(res) {
                    callback && callback(null, res);
                },
                error: function(xhr) {
                    callback && callback(xhr.responseJSON || { message: '获取用户访问令牌失败' });
                }
            });
        }
    };
    
    exports('userApi', userApi);
}); 